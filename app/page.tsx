import {
  <PERSON><PERSON><PERSON>,
  Sparkles,
  Target,
  TrendingUp,
  BookOpen,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";

export default function Home() {
  const features = [
    {
      icon: Sparkles,
      title: "Dynamic Questionnaire",
      description:
        "Answer questions about your interests, personality, and skills to build a comprehensive profile.",
    },
    {
      icon: Target,
      title: "AI-Powered Recommendations",
      description:
        "Receive personalized career path suggestions based on our advanced AI analysis.",
    },
    {
      icon: TrendingUp,
      title: "In-Depth Career Insights",
      description:
        "Explore future outlooks, potential challenges, and summaries for each recommended career.",
    },
    {
      icon: BookOpen,
      title: "Resource Library",
      description:
        "Access curated articles, videos, and courses to support your career exploration journey.",
    },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-surface to-muted">
      {/* Hero Section */}
      <div className="relative overflow-hidden">
        {/* Animated decorative gradient orbs */}
        <div className="absolute top-20 right-10 w-96 h-96 bg-gradient-primary rounded-full opacity-20 blur-3xl animate-float" />
        <div
          className="absolute bottom-20 left-10 w-64 h-64 bg-gradient-energy rounded-full opacity-15 blur-2xl animate-float"
          style={{ animationDelay: "2s" }}
        />

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-32 pb-20">
          <div className="text-center max-w-4xl mx-auto">
            {/* Animated Badge */}
            <div className="inline-flex items-center px-4 py-2 bg-glass backdrop-blur-sm border border-border rounded-full mb-8 animate-bounce-in">
              <Sparkles className="w-4 h-4 text-primary mr-2 animate-pulse-glow" />
              <span className="text-sm font-medium text-foreground">
                AI-Powered Career Discovery
              </span>
            </div>

            {/* Main Heading */}
            <h1 className="text-5xl md:text-7xl font-bold mb-6 leading-tight">
              <span className="bg-gradient-primary bg-clip-text text-transparent">
                Find Your Future
              </span>
              <br />
              <span className="text-foreground">with Career Compass</span>
            </h1>

            {/* Subtitle */}
            <p className="text-xl md:text-2xl text-muted-foreground mb-12 max-w-3xl mx-auto leading-relaxed">
              Our AI-powered assessment helps you discover career paths that
              match your unique personality, interests, and skills. Start your
              journey to a fulfilling career today.
            </p>

            {/* Enhanced CTA Button */}
            <div className="flex flex-col sm:flex-row items-center justify-center gap-4 mb-16">
              <Button
                // onClick={onGetStarted}
                size="lg"
                className="bg-gradient-energy hover:shadow-xl hover:shadow-energy/25 text-white text-lg px-8 py-4 rounded-xl transition-all duration-300 transform hover:scale-105 animate-pulse group"
              >
                Start Your Free Assessment
                <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
              </Button>

              <div className="text-sm text-muted-foreground animate-slide-up">
                ✨ Takes 5-10 minutes • AI-powered insights
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
            Your Personal Career Guide
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            We provide a comprehensive suite of tools to help you navigate the
            complex world of career choices with confidence and clarity.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {features.map(({ icon: Icon, title, description }, index) => (
            <Card
              key={title}
              className="p-6 bg-glass backdrop-blur-sm border border-border hover:shadow-xl hover:-translate-y-2 hover:scale-[1.02] group animate-scale-in transition-all duration-300"
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <div className="w-12 h-12 bg-gradient-primary rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                <Icon className="w-6 h-6 text-white group-hover:rotate-12 transition-transform duration-300" />
              </div>
              <h3 className="text-lg font-semibold text-foreground mb-3 group-hover:text-primary transition-colors">
                {title}
              </h3>
              <p className="text-muted-foreground text-sm leading-relaxed">
                {description}
              </p>
            </Card>
          ))}
        </div>
      </div>

      {/* Final CTA Section */}
      <div className="bg-gradient-to-r from-primary/10 via-accent-blue/10 to-growth/10 py-20">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <h3 className="text-3xl md:text-4xl font-bold text-foreground mb-6">
            Ready to discover your path?
          </h3>
          <p className="text-lg text-muted-foreground mb-8 max-w-2xl mx-auto">
            Join thousands of students and professionals who have found their
            ideal career direction with Career Compass.
          </p>
          <Button
            onClick={onGetStarted}
            size="lg"
            className="bg-gradient-primary hover:shadow-xl hover:shadow-primary/30 text-white text-lg px-8 py-4 rounded-xl transition-all duration-300 hover:scale-105 group"
          >
            Begin Your Journey
            <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
          </Button>
        </div>
      </div>
    </div>
  );
}
