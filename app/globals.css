/* 1. IMPORTS: Modern Tailwind v4 and animation library */
@import "tailwindcss";
@import "tw-animate-css";

/* 2. VARIANTS: Efficient dark mode definition */
@custom-variant dark (&:is(.dark *));

/* 3. BASE STYLES: Your custom body styles and shadcn/ui's base styles */
@layer base {
  /* Your custom theme variables (light mode) */
  :root {
    --background: 0 0% 98%;
    --foreground: 220 15% 15%;
    --surface: 0 0% 100%;
    --card: 0 0% 100%;
    --card-foreground: 220 15% 15%;
    --glass: 0 0% 100% / 0.7;
    --primary: 205 85% 41%;
    --primary-foreground: 0 0% 100%;
    --primary-light: 200 85% 60%;
    --primary-glow: 200 85% 70%;
    --growth: 140 67% 58%;
    --growth-light: 140 67% 70%;
    --accent-blue: 195 100% 50%;
    --energy: 45 93% 58%;
    --energy-light: 45 93% 75%;
    --success: 140 67% 58%;
    --warning: 45 93% 58%;
    --error: 0 70% 60%;
    --muted: 220 10% 95%;
    --muted-foreground: 220 15% 50%;
    --neutral: 220 5% 96%;
    --neutral-foreground: 220 15% 25%;
    --border: 220 10% 90%;
    --input: 220 10% 95%;
    --ring: 205 85% 41%;
    --hover: 200 85% 95%;
    --gradient-primary: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary-light)));
    --gradient-growth: linear-gradient(135deg, hsl(var(--growth)), hsl(var(--growth-light)));
    --gradient-energy: linear-gradient(135deg, hsl(var(--energy)), hsl(var(--energy-light)));
    --gradient-hero: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--accent-blue)));
    --gradient-glass: linear-gradient(145deg, hsl(var(--glass)), hsl(0 0% 100% / 0.4));
    --shadow-soft: 0 4px 12px hsl(220 15% 15% / 0.08);
    --shadow-medium: 0 8px 25px hsl(220 15% 15% / 0.12);
    --shadow-strong: 0 15px 35px hsl(220 15% 15% / 0.15);
    --shadow-glow: 0 0 30px hsl(var(--primary) / 0.3);
    --shadow-energy: 0 0 25px hsl(var(--energy) / 0.4);
    --radius: 1rem;
  }

  /* Your custom theme variables (dark mode) */
  .dark {
    --background: 220 25% 8%;
    --foreground: 0 0% 95%;
    --surface: 220 20% 12%;
    --card: 220 20% 12%;
    --card-foreground: 0 0% 95%;
    --glass: 220 20% 15% / 0.8;
    --primary: 200 85% 60%;
    --primary-foreground: 220 25% 8%;
    --primary-light: 200 85% 70%;
    --growth: 140 67% 65%;
    --growth-light: 140 67% 75%;
    --accent-blue: 195 100% 60%;
    --energy: 45 93% 65%;
    --energy-light: 45 93% 78%;
    --muted: 220 20% 18%;
    --muted-foreground: 220 10% 65%;
    --neutral: 220 15% 15%;
    --neutral-foreground: 220 5% 80%;
    --border: 220 15% 20%;
    --input: 220 15% 18%;
    --ring: 200 85% 60%;
    --hover: 220 20% 15%;
    --gradient-primary: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary-light)));
    --gradient-growth: linear-gradient(135deg, hsl(var(--growth)), hsl(var(--growth-light)));
    --gradient-energy: linear-gradient(135deg, hsl(var(--energy)), hsl(var(--energy-light)));
    --gradient-hero: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--accent-blue)));
    --gradient-glass: linear-gradient(145deg, hsl(var(--glass)), hsl(220 20% 20% / 0.6));
    --shadow-soft: 0 4px 12px hsl(0 0% 0% / 0.25);
    --shadow-medium: 0 8px 25px hsl(0 0% 0% / 0.35);
    --shadow-strong: 0 15px 35px hsl(0 0% 0% / 0.45);
    --shadow-glow: 0 0 30px hsl(var(--primary) / 0.4);
    --shadow-energy: 0 0 25px hsl(var(--energy) / 0.5);
  }

  /* Base styles for the app */
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-family: "Inter", "system-ui", sans-serif;
  }
}

/* 4. TAILWIND THEME CONFIGURATION */

/* This block creates utilities from your CSS variables. Each on a new line. No commas or semicolons. */
@theme inline {
  --color-background: hsl(var(--background))
  --color-foreground: hsl(var(--foreground))
  --color-surface: hsl(var(--surface))
  --color-card: hsl(var(--card))
  --color-card-foreground: hsl(var(--card-foreground))
  --color-primary: hsl(var(--primary))
  --color-primary-foreground: hsl(var(--primary-foreground))
  --color-primary-light: hsl(var(--primary-light))
  --color-primary-glow: hsl(var(--primary-glow))
  --color-growth: hsl(var(--growth))
  --color-growth-light: hsl(var(--growth-light))
  --color-energy: hsl(var(--energy))
  --color-energy-light: hsl(var(--energy-light))
  --color-accent-blue: hsl(var(--accent-blue))
  --color-success: hsl(var(--success))
  --color-warning: hsl(var(--warning))
  --color-error: hsl(var(--error))
  --color-muted: hsl(var(--muted))
  --color-muted-foreground: hsl(var(--muted-foreground))
  --color-neutral: hsl(var(--neutral))
  --color-neutral-foreground: hsl(var(--neutral-foreground))
  --color-glass: hsl(var(--glass))
  --color-hover: hsl(var(--hover))
  --color-border: hsl(var(--border))
  --color-input: hsl(var(--input))
  --color-ring: hsl(var(--ring))
  --radius-sm: calc(var(--radius) - 4px)
  --radius-md: calc(var(--radius) - 2px)
  --radius-lg: var(--radius)
  --radius-xl: calc(var(--radius) + 4px)
  --color-destructive: hsl(var(--error))
}

/* This block configures theme objects using CSS-style nesting */
@theme {
  container {
    center: true;
    padding: 2rem;
    screens {
      '2xl': 1400px;
    }
  }
  backgroundImage {
    'gradient-primary': var(--gradient-primary);
    'gradient-growth': var(--gradient-growth);
    'gradient-energy': var(--gradient-energy);
    'gradient-hero': var(--gradient-hero);
    'gradient-glass': var(--gradient-glass);
  }
  boxShadow {
    soft: var(--shadow-soft);
    medium: var(--shadow-medium);
    strong: var(--shadow-strong);
    glow: var(--shadow-glow);
    energy: var(--shadow-energy);
  }
  keyframes {
    float {
      '0%, 100%': { transform: translateY(0px); }
      '50%': { transform: translateY(-20px); }
    }
    'pulse-glow' {
      '0%': { boxShadow: var(--shadow-glow); }
      '100%': { boxShadow: 0 0 40px hsl(var(--primary) / 0.8); }
    }
    'bounce-in' {
      '0%': { transform: scale(0.3); opacity: 0; }
      '50%': { transform: scale(1.05); opacity: 1; }
      '70%': { transform: scale(0.9); }
      '100%': { transform: scale(1); opacity: 1; }
    }
    'slide-up' {
      '0%': { transform: translateY(30px); opacity: 0; }
      '100%': { transform: translateY(0); opacity: 1; }
    }
    'scale-in' {
      '0%': { transform: scale(0.9); opacity: 0; }
      '100%': { transform: scale(1); opacity: 1; }
    }
    shimmer {
      '0%': { background-position: -200% 0; }
      '100%': { background-position: 200% 0; }
    }
  }
  animation {
    float: float 6s ease-in-out infinite;
    pulse-glow: pulse-glow 2s ease-in-out infinite alternate;
    bounce-in: bounce-in 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    slide-up: slide-up 0.5s ease-out;
    scale-in: scale-in 0.3s ease-out;
    shimmer: shimmer 2s infinite;
  }
}

/* 5. CUSTOM UTILITY CLASSES */
@layer utilities {
  .hover-lift {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
  .hover-lift:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: var(--shadow-strong);
  }
  .gradient-border {
    position: relative;
    background: linear-gradient(hsl(var(--background)), hsl(var(--background))) padding-box,
                linear-gradient(135deg, hsl(var(--primary)), hsl(var(--energy))) border-box;
    border: 2px solid transparent;
  }
}